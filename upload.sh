#!/bin/bash
set -e 

if [ "$1" != "sing" ] && [ "$1" != "us" ]; then
    echo "❌ Invalid parameter: '$1'. Please use 'sing' or 'us'."
    exit 1
fi

if [[ "$2" != *.zip ]]; then
    echo "❌ Local file must have a .zip extension: $3"
    exit 1
fi

if [ "$1" == "sing" ]; then
    BUCKET_NAME="msf-webgl-demo"
else  # Parameter is "us"
    BUCKET_NAME="msf-webgl"
fi

LOCAL_FILE="$2"
bash uploadToS3 "$BUCKET_NAME" "$LOCAL_FILE"
