#!/bin/bash
set -e 

if [ "$1" != "sing" ] && [ "$1" != "us" ]; then
    echo "❌ Invalid parameter: '$1'. Please use 'sing' or 'us'."
    exit 1
fi

if [[ "$2" != *.zip ]]; then
    echo "❌ Local file must have a .zip extension: $3"
    exit 1
fi

if [ "$1" == "sing" ]; then
    BUCKET_NAME="msf-webgl-demo"
    DOMAIN_NAME="https://dev.msf.geargames.com"
else  # Parameter is "us"
    BUCKET_NAME="msf-webgl"
    DOMAIN_NAME="https://msf.geargames.com"
fi

LOCAL_FILE="$2"
echo "🔄 Uploading to s3://$BUCKET_NAME/$LOCAL_FILE with domain: $DOMAIN_NAME" 
bash uploadToS3 "$BUCKET_NAME" "$LOCAL_FILE"  "$DOMAIN_NAME"
