#!/bin/bash
# $1: bucket name
# $2: zip file
# =========[CONFIG]=========
BUCKET_NAME="$1"
ZIP_FILE="$2"
ROLE_ARN="arn:aws:iam::846161807189:role/WPDeveloper"
SESSION_NAME="webgl-deploy-session"

# AWS credentials của IAM user để assume role
export AWS_ACCESS_KEY_ID="********************"
export AWS_SECRET_ACCESS_KEY="9hnkRkGPait/y6tTEpfdZUnkxRiWIRa4PuB/2CD4"
export AWS_DEFAULT_REGION="ap-southeast-1" 
# ==========================

if [ ! -f "$ZIP_FILE" ]; then
  echo "❌ File $ZIP_FILE không tồn tại."
  exit 1
fi

# Lấy build ID
CLIENT_VERSION=$(basename "$ZIP_FILE" .zip | sed -E 's/^webgl_StrikeForce_([^_]+)_.*/\1/')
# Tạo thư mục giải nén
DEST_DIR="$CLIENT_VERSION"
mkdir -p "$DEST_DIR"

TMP_DIR=$(mktemp -d)
unzip -q "$ZIP_FILE" -d "$TMP_DIR"

if [ -d "$TMP_DIR/webgl" ]; then
  mv "$TMP_DIR/webgl/"* "$DEST_DIR/"
else
  echo "❌ Không tìm thấy thư mục webgl/"
  rm -rf "$TMP_DIR"
  exit 1
fi
rm -rf "$TMP_DIR"

# 🔐 Assume role để lấy temporary credentials
echo "🔄 Đang assume role..."
ASSUME_ROLE_OUTPUT=$(aws sts assume-role \
  --role-arn "$ROLE_ARN" \
  --role-session-name "$SESSION_NAME" \
  --output json)

# Trích xuất credentials
TEMP_AK=$(echo "$ASSUME_ROLE_OUTPUT" | jq -r '.Credentials.AccessKeyId')
TEMP_SK=$(echo "$ASSUME_ROLE_OUTPUT" | jq -r '.Credentials.SecretAccessKey')
TEMP_TOKEN=$(echo "$ASSUME_ROLE_OUTPUT" | jq -r '.Credentials.SessionToken')

if [[ -z "$TEMP_AK" || -z "$TEMP_SK" || -z "$TEMP_TOKEN" ]]; then
  echo "❌ Assume role thất bại."
  exit 1
fi

echo "✅ Đã assume role thành công."

# Gán temporary credentials
export AWS_ACCESS_KEY_ID="$TEMP_AK"
export AWS_SECRET_ACCESS_KEY="$TEMP_SK"
export AWS_SESSION_TOKEN="$TEMP_TOKEN"

# Hàm xác định content-type
get_content_type() {
  case "$1" in
    *.js | *.js.gz) echo "application/javascript" ;;
    *.wasm | *.wasm.gz) echo "application/wasm" ;;
    *.json | *.json.gz) echo "application/json" ;;
    *.html | *.html.gz) echo "text/html" ;;
    *.css | *.css.gz) echo "text/css" ;;
    *.png) echo "image/png" ;;
    *.jpg | *.jpeg) echo "image/jpeg" ;;
    *.svg) echo "image/svg+xml" ;;
    *.gz) echo "application/gzip" ;;
    *) echo "binary/octet-stream" ;;
  esac
}

# Upload tất cả file trong $DEST_DIR
echo "🚀 Uploading to s3://$BUCKET_NAME/$CLIENT_VERSION/"
find "$DEST_DIR" -type f | while read -r file; do
  REL_PATH="${file#$DEST_DIR/}"
  KEY="$CLIENT_VERSION/$REL_PATH"

  CONTENT_TYPE=$(get_content_type "$file")
  EXTRA_ARGS=("--content-type" "$CONTENT_TYPE")

  if [[ "$file" == *.gz ]]; then
    EXTRA_ARGS+=("--content-encoding" "gzip")
  fi

  aws s3 cp "$file" "s3://$BUCKET_NAME/$KEY" "${EXTRA_ARGS[@]}"
  echo "✅ Uploaded: $KEY"
done

echo "🎉 Hoàn tất upload với role: $ROLE_ARN"