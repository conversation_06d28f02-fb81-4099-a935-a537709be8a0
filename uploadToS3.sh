#!/bin/bash
# $1: bucket name
# $2: zip file
# $3: domain name
# =========[CONFIG]=========
BUCKET_NAME="$1"
ZIP_FILE="$2"
DOMAIN_NAME="$3"

# AWS credentials
export AWS_ACCESS_KEY_ID="********************"
export AWS_SECRET_ACCESS_KEY="v+BM9drLEdIjCqXxI34e6WkHE3cbPCwotpD9GcaT"
export AWS_DEFAULT_REGION="ap-southeast-1"
# ==========================

if [ ! -f "$ZIP_FILE" ]; then
  echo "❌ File $ZIP_FILE không tồn tại."
  exit 1
fi

if [ -z "$DOMAIN_NAME" ]; then
  echo "❌ Domain name không được để trống."
  echo "Usage: $0 <bucket_name> <zip_file> <domain_name>"
  exit 1
fi

# Lấy build ID
CLIENT_VERSION=$(basename "$ZIP_FILE" .zip | sed -E 's/^webgl_StrikeForce_([^_]+)_.*/\1/')
# Tạo thư mục giải nén
DEST_DIR="$CLIENT_VERSION"
mkdir -p "$DEST_DIR"

TMP_DIR=$(mktemp -d)
unzip -q "$ZIP_FILE" -d "$TMP_DIR"

if [ -d "$TMP_DIR/webgl" ]; then
  mv "$TMP_DIR/webgl/"* "$DEST_DIR/"
else
  echo "❌ Không tìm thấy thư mục webgl/"
  rm -rf "$TMP_DIR"
  exit 1
fi
rm -rf "$TMP_DIR"

# Tìm và thay thế localhost trong file *_ListenerCallback.js
echo "🔄 Đang thay thế localhost với domain: $DOMAIN_NAME"
LISTENER_CALLBACK_FILE=$(find "$DEST_DIR" -name "*_ListenerCallback.js" -type f | head -1)

if [ -n "$LISTENER_CALLBACK_FILE" ]; then
  echo "📝 Tìm thấy file: $LISTENER_CALLBACK_FILE"
  # Thay thế http://localhost:3000 với domain name
  sed -i.bak "s|http://localhost:3000|$DOMAIN_NAME|g" "$LISTENER_CALLBACK_FILE"
  # Xóa file backup
  rm -f "$LISTENER_CALLBACK_FILE.bak"
  echo "✅ Đã thay thế localhost thành $DOMAIN_NAME"
else
  echo "⚠️  Không tìm thấy file *_ListenerCallback.js"
fi

# Hàm xác định content-type
get_content_type() {
  case "$1" in
    *.js | *.js.gz) echo "application/javascript" ;;
    *.wasm | *.wasm.gz) echo "application/wasm" ;;
    *.json | *.json.gz) echo "application/json" ;;
    *.html | *.html.gz) echo "text/html" ;;
    *.css | *.css.gz) echo "text/css" ;;
    *.png) echo "image/png" ;;
    *.jpg | *.jpeg) echo "image/jpeg" ;;
    *.svg) echo "image/svg+xml" ;;
    *.gz) echo "application/gzip" ;;
    *) echo "binary/octet-stream" ;;
  esac
}

# Upload tất cả file trong $DEST_DIR
# echo "🚀 Uploading to s3://$BUCKET_NAME/$CLIENT_VERSION/"
# find "$DEST_DIR" -type f | while read -r file; do
#   REL_PATH="${file#$DEST_DIR/}"
#   KEY="$CLIENT_VERSION/$REL_PATH"

#   CONTENT_TYPE=$(get_content_type "$file")
#   EXTRA_ARGS=("--content-type" "$CONTENT_TYPE")

#   if [[ "$file" == *.gz ]]; then
#     EXTRA_ARGS+=("--content-encoding" "gzip")
#   fi

#   aws s3 cp "$file" "s3://$BUCKET_NAME/$KEY" "${EXTRA_ARGS[@]}"
#   echo "✅ Uploaded: $KEY"
# done

# echo "🎉 Hoàn tất upload to s3://$BUCKET_NAME/$CLIENT_VERSION/"